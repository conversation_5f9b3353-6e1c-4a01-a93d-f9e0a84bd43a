<#PSScriptInfo
.VERSION 2.7.3
.AUTHOR Adam Frangione
.DESCRIPTION GUI version of the file listing script with enhanced user interface and video duration analysis
.TAGS files directory listing inventory GUI video duration
.COPYRIGHT Copyright (c) 2025, Adam Frangione
#>

#Requires -Version 5.1
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

$pversion = "2.7.3"
$global:totalFiles = 0
$global:totalVideoFiles = 0
$global:totalVideoDurationSeconds = 0
$global:isScanning = $false

# Create the main form
$form = New-Object System.Windows.Forms.Form
# $form.Icon = "PanAura.ico"
$form.Text = "ADAMS - File Listing Tool v$pversion"
$form.Size = New-Object System.Drawing.Size(800, 600)
$form.StartPosition = "CenterScreen"
$form.MinimumSize = New-Object System.Drawing.Size(600, 400)
$form.MaximizeBox = $true

# Create menu strip
$menuStrip = New-Object System.Windows.Forms.MenuStrip
$fileMenu = New-Object System.Windows.Forms.ToolStripMenuItem
$fileMenu.Text = "&File"
$helpMenu = New-Object System.Windows.Forms.ToolStripMenuItem
$helpMenu.Text = "&Help"

$exitMenuItem = New-Object System.Windows.Forms.ToolStripMenuItem
$exitMenuItem.Text = "E&xit"
$exitMenuItem.Add_Click({ $form.Close() })

$aboutMenuItem = New-Object System.Windows.Forms.ToolStripMenuItem
$aboutMenuItem.Text = "&About"
$aboutMenuItem.Add_Click({
    [System.Windows.Forms.MessageBox]::Show(
        "ADAMS File Listing Tool v$pversion`n`nCreated by Adam Frangione`nCopyright (c) 2025`n`nGenerates comprehensive file listings with detailed statistics and video duration analysis.",
        "About",
        [System.Windows.Forms.MessageBoxButtons]::OK,
        [System.Windows.Forms.MessageBoxIcon]::Information
    )
})

$fileMenu.DropDownItems.Add($exitMenuItem)
$helpMenu.DropDownItems.Add($aboutMenuItem)
$menuStrip.Items.Add($fileMenu)
$menuStrip.Items.Add($helpMenu)
$form.MainMenuStrip = $menuStrip
$form.Controls.Add($menuStrip)

# Create main panel
$mainPanel = New-Object System.Windows.Forms.Panel
$mainPanel.Dock = [System.Windows.Forms.DockStyle]::Fill
$mainPanel.Padding = New-Object System.Windows.Forms.Padding(10)
$form.Controls.Add($mainPanel)

# Title label
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Text = "ADAMS - File Listing Generator"
$titleLabel.Font = New-Object System.Drawing.Font("Segoe UI", 16, [System.Drawing.FontStyle]::Bold)
$titleLabel.ForeColor = [System.Drawing.Color]::DarkBlue
$titleLabel.AutoSize = $true
$titleLabel.Location = New-Object System.Drawing.Point(10, 35)
$mainPanel.Controls.Add($titleLabel)

# Folder selection group
$folderGroupBox = New-Object System.Windows.Forms.GroupBox
$folderGroupBox.Text = "Folder Selection"
$folderGroupBox.Location = New-Object System.Drawing.Point(10, 80)
$folderGroupBox.Size = New-Object System.Drawing.Size(750, 80)
$folderGroupBox.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$mainPanel.Controls.Add($folderGroupBox)

$folderLabel = New-Object System.Windows.Forms.Label
$folderLabel.Text = "Target Folder:"
$folderLabel.Location = New-Object System.Drawing.Point(15, 25)
$folderLabel.AutoSize = $true
$folderGroupBox.Controls.Add($folderLabel)

$folderTextBox = New-Object System.Windows.Forms.TextBox
$folderTextBox.Location = New-Object System.Drawing.Point(15, 45)
$folderTextBox.Size = New-Object System.Drawing.Size(600, 23)
$folderTextBox.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$folderTextBox.Text = (Get-Location).Path
$folderGroupBox.Controls.Add($folderTextBox)

$browseButton = New-Object System.Windows.Forms.Button
$browseButton.Text = "Browse..."
$browseButton.Location = New-Object System.Drawing.Point(625, 44)
$browseButton.Size = New-Object System.Drawing.Size(100, 25)
$browseButton.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Right
$folderGroupBox.Controls.Add($browseButton)

# Options group
$optionsGroupBox = New-Object System.Windows.Forms.GroupBox
$optionsGroupBox.Text = "Scan Options"
$optionsGroupBox.Location = New-Object System.Drawing.Point(10, 170)
$optionsGroupBox.Size = New-Object System.Drawing.Size(750, 80)
$optionsGroupBox.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$mainPanel.Controls.Add($optionsGroupBox)

$includeSubfoldersCheckBox = New-Object System.Windows.Forms.CheckBox
$includeSubfoldersCheckBox.Text = "Include subfolders"
$includeSubfoldersCheckBox.Location = New-Object System.Drawing.Point(15, 25)
$includeSubfoldersCheckBox.AutoSize = $true
$includeSubfoldersCheckBox.Checked = $true
$optionsGroupBox.Controls.Add($includeSubfoldersCheckBox)

$openFileCheckBox = New-Object System.Windows.Forms.CheckBox
$openFileCheckBox.Text = "Open text file when complete"
$openFileCheckBox.Location = New-Object System.Drawing.Point(200, 25)
$openFileCheckBox.AutoSize = $true
$openFileCheckBox.Checked = $true
$optionsGroupBox.Controls.Add($openFileCheckBox)

$includeModifiedDateCheckBox = New-Object System.Windows.Forms.CheckBox
$includeModifiedDateCheckBox.Text = "Include last modified date/time"
$includeModifiedDateCheckBox.Location = New-Object System.Drawing.Point(15, 50)
$includeModifiedDateCheckBox.AutoSize = $true
$includeModifiedDateCheckBox.Checked = $false
$optionsGroupBox.Controls.Add($includeModifiedDateCheckBox)

$includeVideoDurationCheckBox = New-Object System.Windows.Forms.CheckBox
$includeVideoDurationCheckBox.Text = "Include video file duration"
$includeVideoDurationCheckBox.Location = New-Object System.Drawing.Point(400, 25)
$includeVideoDurationCheckBox.AutoSize = $true
$includeVideoDurationCheckBox.Checked = $true
$optionsGroupBox.Controls.Add($includeVideoDurationCheckBox)

$useShortPathCheckBox = New-Object System.Windows.Forms.CheckBox
$useShortPathCheckBox.Text = "Show relative paths (instead of full paths)"
$useShortPathCheckBox.Location = New-Object System.Drawing.Point(400, 50)
$useShortPathCheckBox.AutoSize = $true
$useShortPathCheckBox.Checked = $true
$optionsGroupBox.Controls.Add($useShortPathCheckBox)

$includeFileSizeCheckBox = New-Object System.Windows.Forms.CheckBox
$includeFileSizeCheckBox.Text = "Include file sizes"
$includeFileSizeCheckBox.Location = New-Object System.Drawing.Point(200, 50)
$includeFileSizeCheckBox.AutoSize = $true
$includeFileSizeCheckBox.Checked = $true
$optionsGroupBox.Controls.Add($includeFileSizeCheckBox)

# Progress group
$progressGroupBox = New-Object System.Windows.Forms.GroupBox
$progressGroupBox.Text = "Progress"
$progressGroupBox.Location = New-Object System.Drawing.Point(10, 260)
$progressGroupBox.Size = New-Object System.Drawing.Size(750, 80)
$progressGroupBox.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$mainPanel.Controls.Add($progressGroupBox)

$progressBar = New-Object System.Windows.Forms.ProgressBar
$progressBar.Location = New-Object System.Drawing.Point(15, 25)
$progressBar.Size = New-Object System.Drawing.Size(720, 23)
$progressBar.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$progressGroupBox.Controls.Add($progressBar)

$statusLabel = New-Object System.Windows.Forms.Label
$statusLabel.Text = "Ready to scan..."
$statusLabel.Location = New-Object System.Drawing.Point(15, 55)
$statusLabel.AutoSize = $true
$statusLabel.ForeColor = [System.Drawing.Color]::DarkGreen
$progressGroupBox.Controls.Add($statusLabel)

# Results area
$resultsGroupBox = New-Object System.Windows.Forms.GroupBox
$resultsGroupBox.Text = "Results"
$resultsGroupBox.Location = New-Object System.Drawing.Point(10, 350)
$resultsGroupBox.Size = New-Object System.Drawing.Size(750, 150)
$resultsGroupBox.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$mainPanel.Controls.Add($resultsGroupBox)

$resultsTextBox = New-Object System.Windows.Forms.TextBox
$resultsTextBox.Multiline = $true
$resultsTextBox.ScrollBars = [System.Windows.Forms.ScrollBars]::Vertical
$resultsTextBox.Location = New-Object System.Drawing.Point(15, 25)
$resultsTextBox.Size = New-Object System.Drawing.Size(720, 115)
$resultsTextBox.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$resultsTextBox.ReadOnly = $true
$resultsTextBox.BackColor = [System.Drawing.Color]::White
$resultsGroupBox.Controls.Add($resultsTextBox)

# Control buttons
$buttonPanel = New-Object System.Windows.Forms.Panel
$buttonPanel.Height = 50
$buttonPanel.Dock = [System.Windows.Forms.DockStyle]::Bottom
$mainPanel.Controls.Add($buttonPanel)

$scanButton = New-Object System.Windows.Forms.Button
$scanButton.Text = "Start Scan"
$scanButton.Size = New-Object System.Drawing.Size(100, 35)
$scanButton.Location = New-Object System.Drawing.Point(10, 10)
$scanButton.BackColor = [System.Drawing.Color]::LightGreen
$buttonPanel.Controls.Add($scanButton)

$cancelButton = New-Object System.Windows.Forms.Button
$cancelButton.Text = "Cancel"
$cancelButton.Size = New-Object System.Drawing.Size(100, 35)
$cancelButton.Location = New-Object System.Drawing.Point(120, 10)
$cancelButton.Enabled = $false
$buttonPanel.Controls.Add($cancelButton)

$openTxtButton = New-Object System.Windows.Forms.Button
$openTxtButton.Text = "Open TXT"
$openTxtButton.Size = New-Object System.Drawing.Size(100, 35)
$openTxtButton.Location = New-Object System.Drawing.Point(230, 10)
$openTxtButton.Enabled = $false
$buttonPanel.Controls.Add($openTxtButton)

$openCsvButton = New-Object System.Windows.Forms.Button
$openCsvButton.Text = "Open CSV"
$openCsvButton.Size = New-Object System.Drawing.Size(100, 35)
$openCsvButton.Location = New-Object System.Drawing.Point(340, 10)
$openCsvButton.Enabled = $false
$buttonPanel.Controls.Add($openCsvButton)

$openFolderButton = New-Object System.Windows.Forms.Button
$openFolderButton.Text = "Open Folder"
$openFolderButton.Size = New-Object System.Drawing.Size(100, 35)
$openFolderButton.Location = New-Object System.Drawing.Point(450, 10)
$openFolderButton.Enabled = $false
$buttonPanel.Controls.Add($openFolderButton)

# Global variables for file paths
$global:outputFile = ""
$global:csvFile = ""

# Helper function to format file sizes
function Get-FormattedFileSize {
    param([long]$sizeInBytes)

    if ($sizeInBytes -ge 1GB) {
        $sizeGB = [math]::Round($sizeInBytes / 1GB, 2)
        return "$sizeGB GB"
    }
    elseif ($sizeInBytes -ge 1MB) {
        $sizeMB = [math]::Round($sizeInBytes / 1MB, 2)
        return "$sizeMB MB"
    }
    elseif ($sizeInBytes -ge 1KB) {
        $sizeKB = [math]::Round($sizeInBytes / 1KB, 2)
        return "$sizeKB KB"
    }
    else {
        return "$sizeInBytes bytes"
    }
}

# Helper function to get video duration
function Get-VideoDuration {
    param([string]$filePath)

    try {
        $shell = New-Object -ComObject Shell.Application
        $folder = $shell.Namespace((Split-Path $filePath -Parent))
        $file = $folder.ParseName((Split-Path $filePath -Leaf))
        $duration = $folder.GetDetailsOf($file, 27)  # Property 27 is duration

        if ($duration -and $duration.Trim() -ne "") {
            return $duration.Trim()
        }
        return $null
    }
    catch {
        return $null
    }
}

# Helper function to convert duration string to seconds
function Convert-DurationToSeconds {
    param([string]$duration)

    if (-not $duration -or $duration.Trim() -eq "") { return 0 }

    try {
        # Clean the duration string and handle various formats
        $cleanDuration = $duration.Trim()
        $parts = $cleanDuration.Split(':')
        $seconds = 0

        if ($parts.Count -eq 3) {
            # HH:MM:SS format
            $hours = [int]($parts[0] -replace '[^\d]', '')
            $minutes = [int]($parts[1] -replace '[^\d]', '')
            $secs = [int]($parts[2] -replace '[^\d]', '')
            $seconds = ($hours * 3600) + ($minutes * 60) + $secs
        }
        elseif ($parts.Count -eq 2) {
            # MM:SS format
            $minutes = [int]($parts[0] -replace '[^\d]', '')
            $secs = [int]($parts[1] -replace '[^\d]', '')
            $seconds = ($minutes * 60) + $secs
        }
        elseif ($parts.Count -eq 1) {
            # SS format or just numbers
            $secs = [int]($parts[0] -replace '[^\d]', '')
            $seconds = $secs
        }

        return [math]::Max(0, $seconds)
    }
    catch {
        Write-Host "Warning: Could not parse duration '$duration'" -ForegroundColor Yellow
        return 0
    }
}

# Helper function to convert seconds back to duration string
function Convert-SecondsToDuration {
    param([int]$totalSeconds)

    if ($totalSeconds -eq 0) { return "00:00:00" }

    $hours = [math]::Floor($totalSeconds / 3600)
    $minutes = [math]::Floor(($totalSeconds % 3600) / 60)
    $seconds = $totalSeconds % 60

    return ("{0:00}:{1:00}:{2:00}" -f $hours, $minutes, $seconds)
}

# Helper function to check if file is a video file
function Test-IsVideoFile {
    param([string]$extension)

    $videoExtensions = @('.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg', '.m2v', '.ts', '.mts', '.vob')
    return $videoExtensions -contains $extension.ToLower()
}

# Event handlers
$browseButton.Add_Click({
    $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog
    $folderBrowser.Description = "Select a folder to scan"
    $folderBrowser.ShowNewFolderButton = $true
    $folderBrowser.SelectedPath = $folderTextBox.Text

    if ($folderBrowser.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
        $folderTextBox.Text = $folderBrowser.SelectedPath
    }
})

$scanButton.Add_Click({
    if ($global:isScanning) { return }

    $targetFolder = $folderTextBox.Text.Trim()
    if ([string]::IsNullOrWhiteSpace($targetFolder)) {
        $targetFolder = (Get-Location).Path
        $folderTextBox.Text = $targetFolder
    }

    if (-not (Test-Path $targetFolder)) {
        [System.Windows.Forms.MessageBox]::Show(
            "The specified folder does not exist. Please select a valid folder.",
            "Invalid Folder",
            [System.Windows.Forms.MessageBoxButtons]::OK,
            [System.Windows.Forms.MessageBoxIcon]::Error
        )
        return
    }

    Start-FileScan -targetFolder $targetFolder
})

$cancelButton.Add_Click({
    if ($global:isScanning) {
        $global:isScanning = $false
        $statusLabel.Text = "Scan cancelled by user"
        $statusLabel.ForeColor = [System.Drawing.Color]::Red
        $scanButton.Enabled = $true
        $cancelButton.Enabled = $false
        $progressBar.Value = 0
    }
})

$openTxtButton.Add_Click({
    if ($global:outputFile -and (Test-Path $global:outputFile)) {
        Start-Process notepad.exe $global:outputFile
    }
})

$openCsvButton.Add_Click({
    if ($global:csvFile -and (Test-Path $global:csvFile)) {
        Start-Process $global:csvFile
    }
})

$openFolderButton.Add_Click({
    if ($global:outputFile -and (Test-Path $global:outputFile)) {
        $folder = Split-Path $global:outputFile -Parent
        Start-Process explorer.exe $folder
    }
})

# Core scanning function
function Start-FileScan {
    param([string]$targetFolder)

    $global:isScanning = $true
    $global:totalFiles = 0
    $global:totalVideoFiles = 0
    $global:totalVideoDurationSeconds = 0
    $scanButton.Enabled = $false
    $cancelButton.Enabled = $true
    $openTxtButton.Enabled = $false
    $openCsvButton.Enabled = $false
    $openFolderButton.Enabled = $false

    $progressBar.Value = 0
    $statusLabel.Text = "Initializing scan..."
    $statusLabel.ForeColor = [System.Drawing.Color]::Blue
    $resultsTextBox.Clear()

    # Set up output files
    $global:outputFile = Join-Path $targetFolder "FileList.txt"
    $global:csvFile = Join-Path $targetFolder "FileList.csv"

    # Get current script path for exclusion
    $currentScript = $MyInvocation.MyCommand.Path
    if ($currentScript -like "*.exe") {
        $currentScript = [System.Diagnostics.Process]::GetCurrentProcess().MainModule.FileName
    }

    # Remove existing output files
    Remove-Item -Path $global:outputFile, $global:csvFile -ErrorAction SilentlyContinue

    try {
        # Start the scanning process
        $statusLabel.Text = "Scanning folders..."
        $form.Refresh()

        Get-Files -path $targetFolder -includeSubfolders $includeSubfoldersCheckBox.Checked -includeModifiedDate $includeModifiedDateCheckBox.Checked -includeVideoDuration $includeVideoDurationCheckBox.Checked -useShortPath $useShortPathCheckBox.Checked -includeFileSize $includeFileSizeCheckBox.Checked -currentScript $currentScript -rootPath $targetFolder

        if ($global:isScanning) {
            # Add summary
            $statusLabel.Text = "Generating summary..."
            $progressBar.Value = 90
            $form.Refresh()

            Add-Summary -outputFile $global:outputFile

            # Complete
            $progressBar.Value = 100
            $statusLabel.Text = "Scan completed successfully!"
            $statusLabel.ForeColor = [System.Drawing.Color]::DarkGreen

            # Update results display
            $resultsTextBox.Text = "Scan completed!`r`n" +
                                  "Total files found: $global:totalFiles`r`n" +
                                  "Output files created:`r`n" +
                                  "- $global:outputFile`r`n" +
                                  "- $global:csvFile"

            # Enable buttons
            $openTxtButton.Enabled = $true
            $openCsvButton.Enabled = $true
            $openFolderButton.Enabled = $true

            # Auto-open if requested
            if ($openFileCheckBox.Checked) {
                Start-Process notepad.exe $global:outputFile
            }
        }
    }
    catch {
        $statusLabel.Text = "Error during scan: $($_.Exception.Message)"
        $statusLabel.ForeColor = [System.Drawing.Color]::Red
        $resultsTextBox.Text = "Error occurred during scanning:`r`n$($_.Exception.Message)"
    }
    finally {
        $global:isScanning = $false
        $scanButton.Enabled = $true
        $cancelButton.Enabled = $false
    }
}

# File scanning function
function Get-Files {
    param (
        [string]$path,
        [bool]$includeSubfolders,
        [bool]$includeModifiedDate,
        [bool]$includeVideoDuration,
        [bool]$useShortPath,
        [bool]$includeFileSize,
        [string]$currentScript,
        [string]$rootPath,
        [int]$indent = 0
    )

    if (-not $global:isScanning) { return }

    $folder = Get-Item -LiteralPath $path
    $line = '-' * 60
    Add-Content -Path $global:outputFile -Value $line

    # Determine which path to display
    if ($useShortPath -and $path -ne $rootPath) {
        $relativePath = $path.Replace($rootPath, "").TrimStart('\', '/')
        if ([string]::IsNullOrEmpty($relativePath)) {
            $displayPath = "."
        } else {
            $displayPath = $relativePath
        }
    } else {
        $displayPath = $folder.FullName
    }

    Add-Content -Path $global:outputFile -Value (' ' * $indent + "Folder: " + $displayPath)

    # Update status
    $statusLabel.Text = "Scanning: $($folder.Name)"
    $form.Refresh()

    $files = Get-ChildItem -Path $path -File | Where-Object {
        $_.FullName -ne $currentScript -and $_.FullName -ne $global:outputFile -and $_.FullName -ne $global:csvFile
    }

    # Initialize folder totals
    $folderTotalSize = 0
    $folderTotalVideoDurationSeconds = 0
    $folderVideoCount = 0

    Add-Content -Path $global:outputFile -Value (' ' * ($indent + 6) + "Number of files: $($files.Count)")
    Add-Content -Path $global:outputFile -Value ""

    foreach ($file in $files) {
        if (-not $global:isScanning) { return }

        # Calculate appropriate file size unit
        $fileSizeFormatted = Get-FormattedFileSize -sizeInBytes $file.Length

        # Add to folder total size
        $folderTotalSize += $file.Length

        # Get video duration if requested and file is a video
        $videoDuration = $null
        if ($includeVideoDuration -and (Test-IsVideoFile -extension $file.Extension)) {
            $videoDuration = Get-VideoDuration -filePath $file.FullName
            if ($videoDuration) {
                $folderVideoCount++
                $durationSeconds = Convert-DurationToSeconds -duration $videoDuration
                $folderTotalVideoDurationSeconds += $durationSeconds

                # Add to global totals
                $global:totalVideoFiles++
                $global:totalVideoDurationSeconds += $durationSeconds
            }
        }

        # Build file info string with trailing dots
        $fileName = $file.Name
        $maxNameLength = 80
        if ($fileName.Length -gt $maxNameLength) {
            $fileName = $fileName.Substring(0, $maxNameLength - 3) + "..."
        }
        $dotsNeeded = $maxNameLength - $fileName.Length
        $dots = "." * $dotsNeeded
        $fileInfo = "$fileName$dots"

        if ($includeFileSize) {
            $fileInfo += " | Size: $fileSizeFormatted"
        }

        if ($includeModifiedDate) {
            $fileInfo += " | Modified: $($file.LastWriteTime)"
        }

        if ($videoDuration) {
            $fileInfo += " | Duration: $videoDuration"
        }

        Add-Content -Path $global:outputFile -Value (' ' * ($indent + 6) + $fileInfo)
        $global:totalFiles++
    }

    # Add folder summary
    Add-Content -Path $global:outputFile -Value ""
    Add-Content -Path $global:outputFile -Value (' ' * ($indent + 6) + "FOLDER SUMMARY:")

    if ($includeFileSize) {
        $folderSizeFormatted = Get-FormattedFileSize -sizeInBytes $folderTotalSize
        Add-Content -Path $global:outputFile -Value (' ' * ($indent + 6) + "Total folder size: $folderSizeFormatted")
    }

    if ($includeVideoDuration -and $folderVideoCount -gt 0) {
        $totalVideoDuration = Convert-SecondsToDuration -totalSeconds $folderTotalVideoDurationSeconds
        Add-Content -Path $global:outputFile -Value (' ' * ($indent + 6) + "Total video files: $folderVideoCount")
        Add-Content -Path $global:outputFile -Value (' ' * ($indent + 6) + "Total video duration: $totalVideoDuration")
    }

    Add-Content -Path $global:outputFile -Value ""

    # Export to CSV
    foreach ($file in $files) {
        if (-not $global:isScanning) { return }

        # Calculate file size values for CSV
        $sizeBytes = $file.Length
        $sizeMB = [math]::Round($file.Length/1MB, 3)
        $sizeGB = [math]::Round($file.Length/1GB, 4)

        # Get video duration if requested and file is a video
        $videoDuration = ""
        if ($includeVideoDuration -and (Test-IsVideoFile -extension $file.Extension)) {
            $duration = Get-VideoDuration -filePath $file.FullName
            if ($duration) {
                $videoDuration = $duration
            }
        }

        # Determine which path to use for CSV
        if ($useShortPath -and $path -ne $rootPath) {
            $csvFolderPath = $path.Replace($rootPath, "").TrimStart('\', '/')
            if ([string]::IsNullOrEmpty($csvFolderPath)) {
                $csvFolderPath = "."
            }
        } else {
            $csvFolderPath = $path
        }

        # Create CSV object with conditional properties
        $csvObject = [PSCustomObject]@{
            Folder = $csvFolderPath
            FileName = $file.Name
            Extension = $file.Extension
        }

        if ($includeFileSize) {
            $csvObject | Add-Member -MemberType NoteProperty -Name "SizeBytes" -Value $sizeBytes
            $csvObject | Add-Member -MemberType NoteProperty -Name "SizeMB" -Value $sizeMB
            $csvObject | Add-Member -MemberType NoteProperty -Name "SizeGB" -Value $sizeGB
        }

        if ($includeModifiedDate) {
            $csvObject | Add-Member -MemberType NoteProperty -Name "LastModified" -Value $file.LastWriteTime
        }

        if ($includeVideoDuration) {
            $csvObject | Add-Member -MemberType NoteProperty -Name "Duration" -Value $videoDuration
        }

        $csvObject | Export-Csv -Path $global:csvFile -Append -NoTypeInformation -Encoding UTF8
    }

    # Process subdirectories if requested
    if ($includeSubfolders) {
        $subDirs = Get-ChildItem -Path $path -Directory
        $dirCount = $subDirs.Count
        $currentDir = 0

        foreach ($dir in $subDirs) {
            if (-not $global:isScanning) { return }

            $currentDir++
            $percentComplete = [math]::Min(80, [math]::Round(($currentDir / [math]::Max(1, $dirCount)) * 80))
            $progressBar.Value = $percentComplete

            Get-Files -path $dir.FullName -includeSubfolders $includeSubfolders -includeModifiedDate $includeModifiedDate -includeVideoDuration $includeVideoDuration -useShortPath $useShortPath -includeFileSize $includeFileSize -currentScript $currentScript -rootPath $rootPath -indent ($indent + 6)
        }
    }
}

# Summary generation function
function Add-Summary {
    param([string]$outputFile)

    if (-not (Test-Path $global:csvFile)) { return }

    $allFiles = Import-Csv -Path $global:csvFile
    $fileTypes = $allFiles | Group-Object -Property Extension | Sort-Object -Property Count -Descending

    # Check if size information is available
    $hasSizeInfo = $allFiles.Count -gt 0 -and ($allFiles[0] | Get-Member -Name "SizeBytes" -ErrorAction SilentlyContinue)

    # Add total to top of file
    $lines = Get-Content $outputFile
    $lines = @("Total number of files: $global:totalFiles", "") + $lines
    Set-Content -Path $outputFile -Value $lines

    # Add summary section
    Add-Content -Path $outputFile -Value "`n`n$('-' * 95)"
    Add-Content -Path $outputFile -Value "$('-' * 95)"
    Add-Content -Path $outputFile -Value "$('-' * 42)  SUMMARY  $('-' * 42)"
    Add-Content -Path $outputFile -Value "$('-' * 95)"
    Add-Content -Path $outputFile -Value "$('-' * 95)"
    Add-Content -Path $outputFile -Value "Total Files: $global:totalFiles"

    if ($hasSizeInfo) {
        $totalSizeBytes = ($allFiles | Measure-Object -Property SizeBytes -Sum).Sum
        $totalSizeMB = [math]::Round($totalSizeBytes / 1MB, 2)
        $totalSizeGB = [math]::Round($totalSizeBytes / 1GB, 2)
        $totalSizeTB = [math]::Round($totalSizeBytes / 1TB, 3)

        # Format total size display
        $totalSizeFormatted = Get-FormattedFileSize -sizeInBytes $totalSizeBytes
        $totalSizeDetails = if ($totalSizeGB -ge 1) {
            "$totalSizeFormatted ($totalSizeMB MB / $totalSizeGB GB"
            if ($totalSizeTB -ge 1) { " / $totalSizeTB TB" }
            ")"
        } elseif ($totalSizeMB -ge 1) {
            "$totalSizeFormatted ($totalSizeMB MB)"
        } else {
            $totalSizeFormatted
        }
        Add-Content -Path $outputFile -Value "Total Size: $totalSizeDetails"
    }

    Add-Content -Path $outputFile -Value "`nFile Types:"

    foreach ($type in $fileTypes) {
        if ($hasSizeInfo) {
            $typeSizeBytes = ($allFiles | Where-Object Extension -eq $type.Name | Measure-Object -Property SizeBytes -Sum).Sum
            $typeSizeFormatted = Get-FormattedFileSize -sizeInBytes $typeSizeBytes
            Add-Content -Path $outputFile -Value "  $($type.Name): $($type.Count) files - $typeSizeFormatted"
        } else {
            Add-Content -Path $outputFile -Value "  $($type.Name): $($type.Count) files"
        }
    }

    # Add largest files section (top 10) only if size info is available
    if ($hasSizeInfo) {
        Add-Content -Path $outputFile -Value "`nLargest Files (Top 10):"
        $largestFiles = $allFiles | Sort-Object -Property {[long]$_.SizeBytes} -Descending | Select-Object -First 10
        foreach ($file in $largestFiles) {
            $fileSizeFormatted = Get-FormattedFileSize -sizeInBytes ([long]$file.SizeBytes)
            Add-Content -Path $outputFile -Value "  $($file.FileName) - $fileSizeFormatted"
        }
    }

    # Add overall video summary if video duration was included
    if ($global:totalVideoFiles -gt 0) {
        $totalVideoDurationFormatted = Convert-SecondsToDuration -totalSeconds $global:totalVideoDurationSeconds
        Add-Content -Path $outputFile -Value "`nOverall Video Summary:"
        Add-Content -Path $outputFile -Value "  Total video files across all folders: $global:totalVideoFiles"
        Add-Content -Path $outputFile -Value "  Total combined video duration: $totalVideoDurationFormatted"
    }
}

# Show the form
[System.Windows.Forms.Application]::EnableVisualStyles()
$form.ShowDialog()



# SIG # Begin signature block
# MIIFagYJKoZIhvcNAQcCoIIFWzCCBVcCAQExCzAJBgUrDgMCGgUAMGkGCisGAQQB
# gjcCAQSgWzBZMDQGCisGAQQBgjcCAR4wJgIDAQAABBAfzDtgWUsITrck0sYpfvNR
# AgEAAgEAAgEAAgEAAgEAMCEwCQYFKw4DAhoFAAQU0LH7ZHgprJRvvP/rQULuEkDV
# MHCgggMGMIIDAjCCAeqgAwIBAgIQdki1sHJ7ZYBL6eT5Lhx9NzANBgkqhkiG9w0B
# AQsFADAZMRcwFQYDVQQDDA5BZGFtIEZyYW5naW9uZTAeFw0yNTA2MzAwNTQxMDRa
# Fw0yODA2MzAwNTUxMDRaMBkxFzAVBgNVBAMMDkFkYW0gRnJhbmdpb25lMIIBIjAN
# BgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1gKfXsCVPFgBW8nCKg0xPJhOABup
# O9VlFdE7eT/5unKUeK80mHsWgC+cIjU8mLCv0uVwhit+8De0tVgtXqXe5YwlbUC4
# jpb9zgnTmm3RsTLi4FLzw2MaZE9PzC25mn1SPSwS6WSb44AtJC+6TcGGCigyvaaJ
# 2siTDHltWXwT7hDdeZ6R+EP6vHel+0k+e/MP4WT98CyKEnyS2NcenUXSq2LF7MVL
# OhytRLoMkUcC4VdZtC3yorMbzct0DQS/86HkRp1S2QBkQhumXWPTmuCHxw9kIrx+
# k/tsQrqXShLrBLcXy76NHOAMP+g7MoWtq00MUrGUlRWNlzRxEdgWeAwioQIDAQAB
# o0YwRDAOBgNVHQ8BAf8EBAMCB4AwEwYDVR0lBAwwCgYIKwYBBQUHAwMwHQYDVR0O
# BBYEFLQHBmgIij5VuQC+Vbx4b9RbwgxzMA0GCSqGSIb3DQEBCwUAA4IBAQCmimgP
# OTLE483gefFaGvVYXXbSyvUgdm3slDec/lBsCn/bVlB2VZZ0xN2NqCgnNl7fdVol
# 1pUqixl7K+EyukR1EST/uLHKvzycpXFxQAuJQzBu4E2jCNDfSsF2OF3bCoNlPdlM
# 1l0JvjvRE980zAT8btN9yHX4IqklIXpKuzICvSZCqq3Nn0nYgeQE13qoXXgtuEc1
# YuDnFtf+9yAr6eMAQ53m/d19H5+lsQgqdXuTvzhYJcyqWOrFvzsQmUzfcfhtT4sP
# CqR5JwC5CbF0xgAVui802nrEQ013j/JjrNlosmKqJksBWLwlBgz3YbnDF5ttRd+L
# 4hvjd2qwAKw819ddMYIBzjCCAcoCAQEwLTAZMRcwFQYDVQQDDA5BZGFtIEZyYW5n
# aW9uZQIQdki1sHJ7ZYBL6eT5Lhx9NzAJBgUrDgMCGgUAoHgwGAYKKwYBBAGCNwIB
# DDEKMAigAoAAoQKAADAZBgkqhkiG9w0BCQMxDAYKKwYBBAGCNwIBBDAcBgorBgEE
# AYI3AgELMQ4wDAYKKwYBBAGCNwIBFTAjBgkqhkiG9w0BCQQxFgQUgKBdJ/Y5HuEr
# N6tFjOGvxCFPzBQwDQYJKoZIhvcNAQEBBQAEggEAnWTe7hYGKKgB12ivcVLvGmDV
# ZEBm4BsinqITyq9u9UdjEE/Lv+I8BKDCrevyUfBV5e+/kLV7NU4XUsN4hztKTO3o
# 5g7pXPOVtO9fZNWyhQRLYhi/xolK60lGPB7rvddY1QOM4B6H+uizOKAP8nxNkrcO
# rW40Surn/tPJ5C1u5rWzWmnALQDiFluXawT/Xwq4wOdW2Y7BCz56PVtU0/dib1A6
# 4sI1e1FkAgzCwIJGSEOVdjacnj6YT/3k2pCtzcuifp1KRJv1eX73uqW5Ip3QuKxf
# ga6H786XgrkUrO/gI4JzMSzt9toS23JDO2PBig8SUQB5v6hRgBXpMh6w0+tO7w==
# SIG # End signature block
